<template>
  <div class="list-card list-common-table">
    <!-- 卡片列表 -->
    <div class="list-card-items">

      <t-form
        ref="form"
        :data="formData"
        :label-width="80"
        @reset="onReset"
        @submit="onSubmit"
        layout="inline"
        :style="{ marginBottom: '30px' }"
      >
          <t-button @click="openUrl('add')" style="margin-right: 20px" class="create-btn">
            新建平台
          </t-button>
        <t-form-item label="平台名称" name="search">
          <t-input
            v-model="formData.search"
            class="form-item-content"
            type="search"
            placeholder="请输入平台名称"
            :style="{ minWidth: '134px' }"
          />
        </t-form-item>

        <t-form-item label="平台管理员" name="admin_name" v-if="$store.state.user.admin==1">
          <t-input
            v-model="formData.admin_name"
            class="form-item-content"
            placeholder="请输入平台管理员"
            :style="{ minWidth: '134px' }"
          />
        </t-form-item>

          <t-button theme="primary" type="submit" :style="{ marginLeft: '8px' }" class="search-btn">
            查询
          </t-button>
          <t-button type="reset" variant="base" theme="default" style="margin-left: 10px" class="reset-btn">
           重置
          </t-button>
      </t-form>
      <t-tabs :value="tabIndex" size="medium" @change="tabChange" class="custom-tabs">
        <t-tab-panel value="0" label="全部">
        </t-tab-panel>
        <t-tab-panel value="1" label="回收站">
        </t-tab-panel>
      </t-tabs>
      <div style="height: 10px"></div>
      <t-row :gutter="[16, 16]">
        <t-col v-for="(item,index) in list"
               :lg="5"
               :xs="6"
               :xl="3"
        >
          <t-card :title="item.platform_name" :description="item.content" class="platform-card">
            <template #avatar>
              <t-avatar :image="item.platform_img" size="56px" class="platform-avatar"></t-avatar>
            </template>
            <template #actions v-if="tabIndex==0">
              <t-dropdown :options="item.platform_type==2?options:options2" :min-column-width="112"
                          @click="clickHandler($event,item)">
                <div class="tdesign-demo-dropdown-trigger">
                  <t-button variant="text" shape="square" class="action-btn">
                    <more-icon/>
                  </t-button>
                </div>
              </t-dropdown>
            </template>
            <template #content>
              <p class="list-card-item_detail--name">
                <span>过期时间：{{ item.overdue }}</span>
              </p>
              <p class="list-card-item_detail--desc" style="margin-top: 10px">
                <span>管理员：{{ item.user_name }}</span>
              </p>
              <div  class="list-card-item_detail--desc" style="margin-top: 10px;height: 25px;">
                <div v-if="item.is_dol && tabIndex==0">
                  <span style="vertical-align: middle">版本：</span>
                  <t-tag variant="light" class="version-tag">v{{ item.is_dol.version }}</t-tag>
                  <t-button @click="checkStatus(item,index)" v-if="item.is_dol.status==0" size="small"
                            style="margin-left: 10px;cursor: pointer" class="status-btn loading">
                    上传中...（点击刷新）
                  </t-button>
                  <t-button v-if="item.is_dol.status==1" size="small" theme="success" style="margin-left: 10px" class="status-btn success">
                   上传成功
                  </t-button>
                  <t-button @click="openError(item.is_dol)" v-if="item.is_dol.status==2" size="small" theme="danger"
                            style="margin-left: 10px" class="status-btn error">
                   上传错误(点击查看)
                  </t-button>
                </div>
              </div>
            </template>
            <template #footer>
              <t-row justify="space-between" align="center">
                <t-col :span="6">
                  <div class="mode-info">
<!--                    <img :src="item.mode_icon" alt="" width="20px" height="20px"-->
<!--                         style="vertical-align: middle;border-radius: 50%"/>-->
                    <span style="vertical-align: middle;margin-left: 5px">
                      {{ item.mode_name }}
                    </span>
                  </div>
                </t-col>
                <t-col :span="6">
                  <div style="text-align: right">
                    <t-button v-if="tabIndex==0" @click="openBack(item)"
                              class="enter-btn">
                     进入后台
                    </t-button>
                    <t-button v-if="tabIndex==1" @click="openHuifu(item)"
                              class="recover-btn">
                      恢复平台
                    </t-button>
                  </div>
                </t-col>
              </t-row>
            </template>
          </t-card>
        </t-col>
      </t-row>
      <div style="margin-top: 30px">
        <t-pagination
          :total="total"
          :page-size="8"
          @current-change="onCurrentChange"
          :showPageSize="false"
          class="custom-pagination"
        ></t-pagination>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import {MessagePlugin} from 'tdesign-vue';
import {
  HeartIcon, ChatIcon, ShareIcon, MoreIcon, AddIcon, SearchIcon,
  EditIcon, DeleteIcon, UploadIcon, SettingIcon, UserIcon,
  CalendarIcon, CheckCircleIcon, ErrorCircleIcon,
  LoadingIcon, RefreshIcon, HomeIcon, RemoveIcon
} from 'tdesign-icons-vue';

export default {
  name: 'ListCard',
  components: {
    HeartIcon,
    ChatIcon,
    ShareIcon,
    MoreIcon,
    AddIcon,
    SearchIcon,
    EditIcon,
    DeleteIcon,
    UploadIcon,
    SettingIcon,
    UserIcon,
    CalendarIcon,
    CheckCircleIcon,
    ErrorCircleIcon,
    LoadingIcon,
    RefreshIcon,
    HomeIcon,
    RemoveIcon
  },
  data() {
    return {
      total: 0,
      page: 1,
      formData: {
        search: '',
        admin_name: ''
      },
      tabIndex: "0",
      list: '',
      options: [
        {
          content: '编辑',
          value: 1,
        },
        {
          content: '删除',
          value: 2,
        }, {
          content: '上传小程序',
          value: 3,
        },
      ],
      options2: [
        {
          content: '编辑',
          value: 1,
        },
        {
          content: '删除',
          value: 2,
        },
      ],
    };
  },

  mounted() {
    this.getList();
  },
  methods: {
    openHuifu(item) {
      var that = this;
      var dle = this.$dialog({
        body: '确定要恢复吗？',
        onConfirm: ({e}) => {
          that.Del(item);
          dle.hide();
        },
      });
    },
    checkStatus(item, i) {
      var list = this.list;
      this.$request
        .post("/Platform/getCallList", {
          id: item.is_dol.id
        })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success('刷新成功！');
            list[i].is_dol.status = res.data.status;
            list[i].is_dol.reason = res.data.reason;
          } else {
            this.message.error(res.msg);
          }
        })
        .catch((e) => {
          console.log(e);
        });
    },
    openBack(item) {
      window.location.href = "index.php?s=/cloud/platform/execute.html&uniacid=" + item.id + "&token=" + this.$store.state.user.token;
    },
    openError(item) {
      var Error = this.$dialog({
        body: item.reason,
        cancelBtn: null,
        onConfirm: ({e}) => {
          Error.hide();
        },
      });
    },
    clickHandler(i, d) {
      if (i.value == 1) {
        this.$router.push('edit?id=' + d.id);
        return;
      }
      if (i.value == 2) {
        if (d.platform_type == 1) {
          this.$message.error('该平台为公众平台');
          return;
        }
        var that = this;
        var dle = this.$dialog({
          body: '确定要删除吗？',
          onConfirm: ({e}) => {
            that.Del(d);
            dle.hide();
          },
        });
      } else {
        this.$router.push('upload?id=' + d.id);
      }

    },
    Del(item) {
      this.$request
        .post("/Platform/DelPlatform", {
          id: item.id,
          type: item.is_del
        })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success(res.msg);
          } else {
            this.$message.error(res.msg);
          }
          this.getList();
        })
        .catch((e) => {
          console.log(e);
        });
    },
    openUrl(url) {
      this.$router.push(url);
    },
    getList() {
      this.$request
        .post("/Index/get_home", {
          type: this.tabIndex,
          page: this.page,
          search: this.formData.search,
          admin_name: this.formData.admin_name
        })
        .then((res) => {

          this.list = res.list;
          this.total = res.count;
        })
        .catch((e) => {
          console.log(e);
        });
    },
    onCurrentChange(d) {
      this.page = d;
      this.getList();
    },
    tabChange(d) {
      this.tabIndex = d;
      this.page = 1;
      this.getList();
    },
    onSubmit() {
      this.getList();
    },
    onReset() {
      this.page = 1;
      this.formData.search = '';
      this.formData.admin_name = '';
      this.getList();
    }
  }
};
</script>
<style scoped lang="less">
// 页面整体样式
.list-card {
  min-height: 100vh;

  .list-card-items {
    background:  #ffffff;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 8px 24px rgba(33, 150, 243, 0.08);
    backdrop-filter: blur(10px);
  }
}

// 表单区域样式
.t-form {
  background: #f0f8ff;
  padding: 20px;
  border-radius: 15px;
  margin-bottom: 30px !important;
  box-shadow: 0 6px 16px rgba(33, 150, 243, 0.06);

  .t-form-item {
    margin-bottom: 15px;
  }

  .form-item-content {
    border-radius: 10px;
    border: 2px solid transparent;
    transition: all 0.3s ease;

    &:focus {
      border-color: #0052d9;
      box-shadow: 0 0 0 3px rgba(0, 82, 217, 0.1);
    }
  }
}

// 按钮样式优化
.create-btn {
  background: #2196F3 !important;
  border: none !important;
  border-radius: 10px !important;
  color: white !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.25) !important;

  &:hover {
    background: #1976D2 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 6px 16px rgba(33, 150, 243, 0.35) !important;
  }
}

.search-btn {
  background: #2196F3 !important;
  border: none !important;
  border-radius: 10px !important;
  color: white !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.25) !important;

  &:hover {
    background: #1976D2 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 6px 16px rgba(33, 150, 243, 0.35) !important;
  }
}

.reset-btn {
  background: #f5f5f5 !important;
  border: 1px solid #e0e0e0 !important;
  border-radius: 10px !important;
  color: #666 !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;

  &:hover {
    background: #eeeeee !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  }
}

// 标签页样式
.custom-tabs {
  margin-bottom: 20px;

  .t-tabs__nav {
    background: #f8fbff;
    border-radius: 15px;
    padding: 5px;

    .t-tabs__nav-item {
      border-radius: 10px;
      transition: all 0.3s ease;

      &.t-is-active {
        background: #2196F3;
        color: white;
        box-shadow: 0 3px 8px rgba(33, 150, 243, 0.25);
      }

      &:hover {
        background: rgba(33, 150, 243, 0.08);
      }
    }
  }
}

// 平台卡片样式
.platform-card {
  height: 285px;
  width: 100% !important;
  max-width: 380px !important;
  border-radius: 20px !important;
  border: none !important;
  box-shadow: 0 6px 20px rgba(33, 150, 243, 0.08) !important;
  transition: all 0.3s ease !important;
  background: #ffffff !important;
  overflow: hidden !important;
  border: 1px solid #e3f2fd !important;

  &:hover {
    transform: translateY(-4px) !important;
    box-shadow: 0 12px 28px rgba(33, 150, 243, 0.12) !important;
    border-color: #bbdefb !important;
  }

  .t-card__header {
    background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 100%);
    color: #1976D2;
    padding: 20px;
    margin: -20px -20px 20px -20px;
    border-radius: 20px 20px 0 0;

    .t-card__title {
      color: #1976D2 !important;
      font-weight: 600 !important;
    }

    .t-card__description {
      color: #666 !important;
    }
  }

  .t-card__body {
    padding: 20px;
  }

  .t-card__footer {
    padding: 20px;
    border-top: 1px solid #f0f0f0;
    background: #fafafa;
  }
}

// 头像样式
.platform-avatar {
  border: 3px solid #fff !important;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
}

// 操作按钮样式
.action-btn {
  border-radius: 50% !important;
  transition: all 0.3s ease !important;

  &:hover {
    background: rgba(33, 150, 243, 0.08) !important;
    transform: scale(1.05) !important;
  }
}

// 状态按钮样式
.status-btn {
  border-radius: 20px !important;
  font-weight: 600 !important;
  transition: all 0.3s ease !important;

  &.loading {
    background: #fff8e1 !important;
    color: #f57c00 !important;
    border: 1px solid #ffcc02 !important;

    &:hover {
      background: #fff3c4 !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 3px 8px rgba(245, 124, 0, 0.2) !important;
    }
  }

  &.success {
    background: #e8f5e8 !important;
    color: #2e7d32 !important;
    border: 1px solid #4caf50 !important;

    &:hover {
      background: #c8e6c9 !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 3px 8px rgba(46, 125, 50, 0.2) !important;
    }
  }

  &.error {
    background: #ffebee !important;
    color: #c62828 !important;
    border: 1px solid #f44336 !important;

    &:hover {
      background: #ffcdd2 !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 3px 8px rgba(198, 40, 40, 0.2) !important;
    }
  }
}

// 版本标签样式
.version-tag {
  background: #2196F3 !important;
  color: white !important;
  border-radius: 15px !important;
  font-weight: 500 !important;
}

// 模式信息样式
.mode-info {
  display: flex;
  align-items: center;
  padding: 10px;
  background: #f0f8ff;
  border-radius: 15px;
  color: #1976D2;
  font-weight: 500;
}

// 进入后台按钮样式
.enter-btn {
  background: #2196F3 !important;
  border: none !important;
  border-radius: 20px !important;
  color: white !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.25) !important;

  &:hover {
    background: #1976D2 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 6px 16px rgba(33, 150, 243, 0.35) !important;
  }
}

// 恢复平台按钮样式
.recover-btn {
  background: #4CAF50 !important;
  border: none !important;
  border-radius: 20px !important;
  color: white !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.25) !important;

  &:hover {
    background: #388E3C !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 6px 16px rgba(76, 175, 80, 0.35) !important;
  }
}

// 分页样式
.custom-pagination {
  .t-pagination__total {
    color: #666;
    font-weight: 600;
  }

  .t-pagination__prev,
  .t-pagination__next,
  .t-pagination__item {
    border-radius: 10px;
    transition: all 0.3s ease;

    &:hover {
      background: #2196F3;
      color: white;
      transform: translateY(-1px);
    }

    &.t-is-current {
      background: #2196F3;
      color: white;
      box-shadow: 0 3px 8px rgba(33, 150, 243, 0.25);
    }
  }
}

// 响应式优化
@media (max-width: 768px) {
  .list-card {
    padding: 10px;

    .list-card-items {
      padding: 20px;
    }
  }

  .platform-card {
    width: 100% !important;
    max-width: none !important;
  }

  .t-form {
    padding: 15px;
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.platform-card {
  animation: fadeInUp 0.6s ease-out;
}

// 图标样式优化
.t-icon {
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.1);
  }
}
</style>
