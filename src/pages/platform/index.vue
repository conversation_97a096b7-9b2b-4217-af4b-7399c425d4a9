<template>
  <div class="list-card list-common-table">
    <!-- 卡片列表 -->
    <div class="list-card-items">
      <!-- 表单和标签页区域 -->
      <div class="form-section">
        <!-- 左侧表单区域 -->
        <div class="form-left">
          <t-form ref="form" :data="formData" :label-width="80" @reset="onReset" @submit="onSubmit" layout="inline">
            <t-button @click="openUrl('add')" style="margin-right: 20px" class="create-btn"> 新建平台</t-button>
            <t-form-item label="平台名称" name="search">
              <t-input
                v-model="formData.search"
                class="form-item-content"
                type="search"
                placeholder="请输入平台名称"
                :style="{ minWidth: '134px' }"
              />
            </t-form-item>

            <t-form-item label="平台管理员" name="admin_name" v-if="$store.state.user.admin == 1">
              <t-input
                v-model="formData.admin_name"
                class="form-item-content"
                placeholder="请输入平台管理员"
                :style="{ minWidth: '134px' }"
              />
            </t-form-item>

            <t-button theme="primary" type="submit" :style="{ marginLeft: '8px' }" class="search-btn"> 查询</t-button>
            <t-button type="reset" variant="base" theme="default" style="margin-left: 10px" class="reset-btn">
              重置
            </t-button>
            <!-- 右侧标签页区域 -->
            <t-tabs :value="tabIndex" size="medium" @change="tabChange" class="custom-tabs">
              <t-tab-panel value="0" label="全部"></t-tab-panel>
              <t-tab-panel value="1" label="回收站"></t-tab-panel>
            </t-tabs>
          </t-form>
        </div>
      </div>

      <t-row :gutter="[16, 16]">
        <t-col v-for="(item, index) in list" :lg="5" :xs="6" :xl="3">
          <t-card :title="item.platform_name" :description="item.content" class="platform-card">
            <template #avatar>
              <t-avatar :image="item.platform_img" size="56px" class="platform-avatar"></t-avatar>
            </template>
            <template #actions v-if="tabIndex == 0">
              <t-dropdown
                :options="item.platform_type == 2 ? options : options2"
                :min-column-width="112"
                @click="clickHandler($event, item)"
              >
                <div class="tdesign-demo-dropdown-trigger">
                  <t-button variant="text" shape="square" class="action-btn">
                    <more-icon />
                  </t-button>
                </div>
              </t-dropdown>
            </template>
            <template #content>
              <p class="list-card-item_detail--name">
                <span>过期时间：{{ item.overdue }}</span>
              </p>
              <p class="list-card-item_detail--desc" style="margin-top: 10px">
                <span>管理员：{{ item.user_name }}</span>
              </p>
              <div class="list-card-item_detail--desc" style="margin-top: 10px; height: 25px">
                <div v-if="item.is_dol && tabIndex == 0">
                  <span style="vertical-align: middle">版本：</span>
                  <t-tag variant="light" class="version-tag">v{{ item.is_dol.version }}</t-tag>
                  <t-button
                    @click="checkStatus(item, index)"
                    v-if="item.is_dol.status == 0"
                    size="small"
                    style="margin-left: 10px; cursor: pointer"
                    class="status-btn loading"
                  >
                    上传中...（点击刷新）
                  </t-button>
                  <t-button
                    v-if="item.is_dol.status == 1"
                    size="small"
                    theme="success"
                    style="margin-left: 10px"
                    class="status-btn success"
                  >
                    上传成功
                  </t-button>
                  <t-button
                    @click="openError(item.is_dol)"
                    v-if="item.is_dol.status == 2"
                    size="small"
                    theme="danger"
                    style="margin-left: 10px"
                    class="status-btn error"
                  >
                    上传错误(点击查看)
                  </t-button>
                </div>
              </div>
            </template>
            <template #footer>
              <t-row justify="space-between" align="center">
                <t-col :span="6">
                  <div class="mode-info">
                    <!--                    <img :src="item.mode_icon" alt="" width="20px" height="20px"-->
                    <!--                         style="vertical-align: middle;border-radius: 50%"/>-->
                    <span style="vertical-align: middle; margin-left: 5px">
                      {{ item.mode_name }}
                    </span>
                  </div>
                </t-col>
                <t-col :span="6">
                  <div style="text-align: right">
                    <t-button v-if="tabIndex == 0" @click="openBack(item)" class="enter-btn"> 进入后台</t-button>
                    <t-button v-if="tabIndex == 1" @click="openHuifu(item)" class="recover-btn"> 恢复平台</t-button>
                  </div>
                </t-col>
              </t-row>
            </template>
          </t-card>
        </t-col>
      </t-row>
      <div style="margin-top: 30px">
        <t-pagination
          :total="total"
          :page-size="8"
          @current-change="onCurrentChange"
          :showPageSize="false"
          class="custom-pagination"
        ></t-pagination>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { MessagePlugin } from 'tdesign-vue';
import {
  HeartIcon,
  ChatIcon,
  ShareIcon,
  MoreIcon,
  AddIcon,
  SearchIcon,
  EditIcon,
  DeleteIcon,
  UploadIcon,
  SettingIcon,
  UserIcon,
  CalendarIcon,
  CheckCircleIcon,
  ErrorCircleIcon,
  LoadingIcon,
  RefreshIcon,
  HomeIcon,
  RemoveIcon,
} from 'tdesign-icons-vue';

export default {
  name: 'ListCard',
  components: {
    HeartIcon,
    ChatIcon,
    ShareIcon,
    MoreIcon,
    AddIcon,
    SearchIcon,
    EditIcon,
    DeleteIcon,
    UploadIcon,
    SettingIcon,
    UserIcon,
    CalendarIcon,
    CheckCircleIcon,
    ErrorCircleIcon,
    LoadingIcon,
    RefreshIcon,
    HomeIcon,
    RemoveIcon,
  },
  data() {
    return {
      total: 0,
      page: 1,
      formData: {
        search: '',
        admin_name: '',
      },
      tabIndex: '0',
      list: '',
      options: [
        {
          content: '编辑',
          value: 1,
        },
        {
          content: '删除',
          value: 2,
        },
        {
          content: '上传小程序',
          value: 3,
        },
      ],
      options2: [
        {
          content: '编辑',
          value: 1,
        },
        {
          content: '删除',
          value: 2,
        },
      ],
    };
  },

  mounted() {
    this.getList();
  },
  methods: {
    openHuifu(item) {
      var that = this;
      var dle = this.$dialog({
        body: '确定要恢复吗？',
        onConfirm: ({ e }) => {
          that.Del(item);
          dle.hide();
        },
      });
    },
    checkStatus(item, i) {
      var list = this.list;
      this.$request
        .post('/Platform/getCallList', {
          id: item.is_dol.id,
        })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success('刷新成功！');
            list[i].is_dol.status = res.data.status;
            list[i].is_dol.reason = res.data.reason;
          } else {
            this.message.error(res.msg);
          }
        })
        .catch((e) => {
          console.log(e);
        });
    },
    openBack(item) {
      window.location.href =
        'index.php?s=/cloud/platform/execute.html&uniacid=' + item.id + '&token=' + this.$store.state.user.token;
    },
    openError(item) {
      var Error = this.$dialog({
        body: item.reason,
        cancelBtn: null,
        onConfirm: ({ e }) => {
          Error.hide();
        },
      });
    },
    clickHandler(i, d) {
      if (i.value == 1) {
        this.$router.push('edit?id=' + d.id);
        return;
      }
      if (i.value == 2) {
        if (d.platform_type == 1) {
          this.$message.error('该平台为公众平台');
          return;
        }
        var that = this;
        var dle = this.$dialog({
          body: '确定要删除吗？',
          onConfirm: ({ e }) => {
            that.Del(d);
            dle.hide();
          },
        });
      } else {
        this.$router.push('upload?id=' + d.id);
      }
    },
    Del(item) {
      this.$request
        .post('/Platform/DelPlatform', {
          id: item.id,
          type: item.is_del,
        })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success(res.msg);
          } else {
            this.$message.error(res.msg);
          }
          this.getList();
        })
        .catch((e) => {
          console.log(e);
        });
    },
    openUrl(url) {
      this.$router.push(url);
    },
    getList() {
      this.$request
        .post('/Index/get_home', {
          type: this.tabIndex,
          page: this.page,
          search: this.formData.search,
          admin_name: this.formData.admin_name,
        })
        .then((res) => {
          this.list = res.list;
          this.total = res.count;
        })
        .catch((e) => {
          console.log(e);
        });
    },
    onCurrentChange(d) {
      this.page = d;
      this.getList();
    },
    tabChange(d) {
      this.tabIndex = d;
      this.page = 1;
      this.getList();
    },
    onSubmit() {
      this.getList();
    },
    onReset() {
      this.page = 1;
      this.formData.search = '';
      this.formData.admin_name = '';
      this.getList();
    },
  },
};
</script>
<style scoped lang="less">
// ==================== 现代化SaaS平台管理系统 ====================

// 页面整体样式
.list-card {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 24px;

  .list-card-items {
    background: #ffffff;
    border-radius: 16px;
    padding: 32px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    border: 1px solid #e9ecef;
    position: relative;
  }
}

// 表单和标签页区域布局
.form-section {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 24px;
  margin-bottom: 32px;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
  }
}

.form-left {
  flex: 1;
  min-width: 0;
}

// 表单区域样式
.t-form {
  background: #ffffff;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  margin-bottom: 0 !important;

  .t-form-item {
    margin-bottom: 16px;
  }

  .form-item-content {
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }
}

// 按钮样式现代化
.create-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
  border: none !important;
  border-radius: 8px !important;
  color: white !important;
  font-weight: 600 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2) !important;
  letter-spacing: 0.025em !important;

  &:hover {
    background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
  }
}

.search-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
  border: none !important;
  border-radius: 8px !important;
  color: white !important;
  font-weight: 600 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2) !important;
  letter-spacing: 0.025em !important;

  &:hover {
    background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
  }
}

.reset-btn {
  background: #f8f9fa !important;
  border: 1px solid #e9ecef !important;
  border-radius: 8px !important;
  color: #6b7280 !important;
  font-weight: 500 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;

  &:hover {
    background: #f1f3f4 !important;
    border-color: #d1d5db !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  }
}

// 标签页右侧布局设计
.custom-tabs {
  margin-bottom: 0;

  :deep(.t-tabs__nav) {
    background: #ffffff;
    border-radius: 12px;
    padding: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    gap: 2px;
    display: inline-flex;
    min-width: 200px;

    .t-tabs__nav-item {
      border-radius: 8px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      padding: 12px 20px;
      font-weight: 500;
      font-size: 0.875rem;
      letter-spacing: 0.025em;
      position: relative;
      overflow: hidden;
      flex: 1;
      text-align: center;

      &.t-is-active {
        background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        transform: translateY(-1px);

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
          border-radius: 8px;
          pointer-events: none;
        }
      }

      &:hover:not(.t-is-active) {
        background: #f8f9fa;
        transform: translateY(-1px);
      }
    }
  }
}

// 平台卡片现代化设计
.platform-card {
  height: 285px;
  width: 100% !important;
  max-width: 380px !important;
  border-radius: 16px !important;
  border: 1px solid #e9ecef !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  background: #ffffff !important;
  overflow: hidden !important;
  position: relative !important;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6 0%, #1e40af 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    transform: translateY(-4px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12) !important;
    border-color: #d1d5db !important;

    &::before {
      opacity: 1;
    }
  }

  :deep(.t-card__header) {
    background: #ffffff;
    color: #1f2937;
    padding: 24px;
    margin: -24px -24px 20px -24px;
    border-radius: 16px 16px 0 0;
    border-bottom: 1px solid #f3f4f6;

    .t-card__title {
      color: #1f2937 !important;
      font-weight: 600 !important;
      font-size: 1.125rem !important;
      letter-spacing: -0.025em !important;
    }

    .t-card__description {
      color: #6b7280 !important;
      font-size: 0.875rem !important;
      margin-top: 4px !important;
    }
  }

  :deep(.t-card__body) {
    padding: 24px;
  }

  :deep(.t-card__footer) {
    padding: 24px;
    border-top: 1px solid #f3f4f6;
    background: #f8f9fa;
    margin: 0 -24px -24px -24px;
  }
}

// 头像现代化样式
.platform-avatar {
  border: 2px solid #f1f3f4 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
  transition: all 0.3s ease !important;
}

// 操作按钮现代化
.action-btn {
  border-radius: 8px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  background: #f8f9fa !important;
  border: 1px solid #e9ecef !important;
  position: relative !important;
  overflow: hidden !important;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    transform: scale(1.05) !important;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25) !important;
    border-color: transparent !important;

    &::before {
      opacity: 1;
    }

    :deep(.t-icon) {
      color: white !important;
      position: relative !important;
      z-index: 1 !important;
    }
  }
}

// 状态按钮现代化
.status-btn {
  border-radius: 12px !important;
  font-weight: 600 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  font-size: 0.75rem !important;
  letter-spacing: 0.025em !important;

  &.loading {
    background: #fef3c7 !important;
    color: #d97706 !important;
    border: 1px solid #fbbf24 !important;

    &:hover {
      background: #fde68a !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 3px 8px rgba(217, 119, 6, 0.2) !important;
    }
  }

  &.success {
    background: #d1fae5 !important;
    color: #065f46 !important;
    border: 1px solid #10b981 !important;

    &:hover {
      background: #a7f3d0 !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 3px 8px rgba(6, 95, 70, 0.2) !important;
    }
  }

  &.error {
    background: #fee2e2 !important;
    color: #dc2626 !important;
    border: 1px solid #ef4444 !important;

    &:hover {
      background: #fecaca !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 3px 8px rgba(220, 38, 38, 0.2) !important;
    }
  }
}

// 版本标签现代化
.version-tag {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
  color: white !important;
  border-radius: 12px !important;
  font-weight: 600 !important;
  font-size: 0.75rem !important;
  letter-spacing: 0.025em !important;
  padding: 4px 8px !important;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2) !important;
}

// 模式信息现代化
.mode-info {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: #f3f4f6;
  border-radius: 8px;
  color: #6b7280;
  font-weight: 500;
  font-size: 0.875rem;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;

  &:hover {
    background: #e5e7eb;
    border-color: #d1d5db;
  }
}

// 进入后台按钮现代化
.enter-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
  border: none !important;
  border-radius: 12px !important;
  color: white !important;
  font-weight: 600 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2) !important;
  font-size: 0.875rem !important;
  letter-spacing: 0.025em !important;
  padding: 8px 16px !important;

  &:hover {
    background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
  }
}

// 恢复平台按钮现代化
.recover-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
  border: none !important;
  border-radius: 12px !important;
  color: white !important;
  font-weight: 600 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2) !important;
  font-size: 0.875rem !important;
  letter-spacing: 0.025em !important;
  padding: 8px 16px !important;

  &:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3) !important;
  }
}

// 分页样式
.custom-pagination {
  .t-pagination__total {
    color: #666;
    font-weight: 600;
  }

  .t-pagination__prev,
  .t-pagination__next,
  .t-pagination__item {
    border-radius: 10px;
    transition: all 0.3s ease;

    &:hover {
      background: #2196f3;
      color: white;
      transform: translateY(-1px);
    }

    &.t-is-current {
      background: #2196f3;
      color: white;
      box-shadow: 0 3px 8px rgba(33, 150, 243, 0.25);
    }
  }
}

// 响应式优化
@media (max-width: 768px) {
  .list-card {
    padding: 16px;

    .list-card-items {
      padding: 20px;
      border-radius: 12px;
    }
  }

  .form-section {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .form-right {
    justify-content: center;
    width: 100%;
  }

  .platform-card {
    width: 100% !important;
    max-width: none !important;
    height: auto !important;
    min-height: 260px !important;
  }

  .t-form {
    padding: 20px;
  }

  .custom-tabs {
    :deep(.t-tabs__nav) {
      width: 100%;
      max-width: 300px;

      .t-tabs__nav-item {
        padding: 10px 16px;
        font-size: 0.8rem;
      }
    }
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.platform-card {
  animation: fadeInUp 0.6s ease-out;
}

// 图标样式优化
.t-icon {
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.1);
  }
}
</style>
