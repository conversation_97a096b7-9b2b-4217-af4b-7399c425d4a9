<template>
  <div class="modern-form-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">创建新平台</h1>
    </div>
    <t-form
      :data="formData"
      @reset="onReset"
      :rules="rules"
      @submit="onSubmit"
      :label-width="0"
      class="modern-form"
    >
      <!-- 第一步：基础设置 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">基础设置</h3>
          <p class="section-desc">选择平台类型和基本配置</p>
        </div>

        <div class="form-grid">
          <!-- 平台类型 -->
          <div class="form-group full-width">
            <label class="modern-label">平台类型</label>
            <div class="radio-card-group">
              <div class="radio-card" :class="{ active: formData.mod_type === '1' }" @click="selectPlatformType('1')">
                <div class="radio-content">
                  <h4>公众号平台</h4>
                  <p>微信公众号管理平台</p>
                </div>
                <div class="radio-check" :class="{ checked: formData.mod_type === '1' }">✓</div>
              </div>
              <div class="radio-card" :class="{ active: formData.mod_type === '2' }" @click="selectPlatformType('2')">
                <div class="radio-content">
                  <h4>小程序平台</h4>
                  <p>微信小程序管理平台</p>
                </div>
                <div class="radio-check" :class="{ checked: formData.mod_type === '2' }">✓</div>
              </div>
            </div>
          </div>

          <!-- 管理员专用字段 -->
          <template v-if="$store.state.user.admin==1">
            <div class="form-group">
              <div class="floating-input">
                <label class="floating-label">平台使用者</label>
                <t-select
                  @change="selectUser"
                  v-model="formData.selectValue"
                  placeholder="请选择平台使用者"
                  class="modern-select"
                  size="large"
                >
                  <t-option v-for="item in userList" :value="item.id" :label="item.name" :key="item.id"></t-option>
                </t-select>
              </div>
            </div>

            <div class="form-group" v-if="formData.add_type==0">
              <div class="floating-input">
                <label class="floating-label">平台过期时间</label>
                <t-date-picker
                  :clearable="true"
                  placeholder="请选择平台过期时间"
                  :enableTimePicker="true"
                  :allow-input="false"
                  v-model="formData.days"
                  size="large"
                  class="modern-datepicker"
                />
              </div>
            </div>
          </template>
        </div>
      </div>

      <!-- 第二步：应用配置 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">应用配置</h3>
          <p class="section-desc">选择要使用的应用模块</p>
        </div>

        <div class="form-grid">
          <div class="form-group full-width">
            <label class="modern-label">选择应用</label>
            <div class="app-selection">
              <t-radio-group v-if="list.length>0" v-model="formData.powLimitId" class="app-radio-group">
                <div v-for="item in list" :key="item.value" class="app-option">
                  <t-radio :value="item.value" class="app-radio">
                    <div class="app-info">
                      <span class="app-name">{{ item.label }}</span>
                    </div>
                  </t-radio>
                </div>
              </t-radio-group>
              <div v-if="list.length==0" class="no-apps">
                <div class="no-apps-icon">📭</div>
                <p v-if="formData.add_type==0">暂无可用应用</p>
                <p v-if="formData.add_type==1">没有可创建的应用</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 第三步：详细信息 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">详细信息</h3>
          <p class="section-desc">填写平台的详细配置信息</p>
        </div>

        <div class="form-grid">
          <div class="form-group">
            <div class="floating-input">
              <label class="floating-label">平台名称</label>
              <t-input
                v-model="formData.app_name"
                placeholder="请输入公众号/小程序名称"
                size="large"
                class="modern-input"
              />
            </div>
          </div>

          <div class="form-group">
            <div class="floating-input">
              <label class="floating-label">平台描述</label>
              <t-input
                v-model="formData.desc"
                placeholder="请输入平台描述信息"
                size="large"
                class="modern-input"
              />
            </div>
          </div>

          <div class="form-group">
            <div class="floating-input">
              <label class="floating-label">AppId</label>
              <t-input
                v-model="formData.app_id"
                placeholder="请输入微信AppId"
                size="large"
                class="modern-input"
              />
            </div>
          </div>

          <div class="form-group">
            <div class="floating-input">
              <label class="floating-label">AppSecret</label>
              <t-input
                v-model="formData.app_secret"
                placeholder="请输入微信AppSecret"
                size="large"
                class="modern-input"
                type="password"
              />
            </div>
          </div>

          <div class="form-group" v-if="is==1">
            <div class="floating-input">
              <label class="floating-label">平台域名</label>
              <t-input
                v-model="formData.pigeno_url"
                placeholder="请输入平台域名"
                size="large"
                class="modern-input"
              />
            </div>
          </div>

          <div class="form-group full-width" style="padding-left: 15px;">
            <label class="floating-label">平台图标</label>
            <div class="upload-area">
              <t-upload
                action="/web/index.php?s=/cloud/Modules/UploadImg"
                :headers="{token:$store.state.user.token}"
                v-model="formData.img_icon"
                theme="image"
                tips="拖拽或点击上传平台图标"
                accept="image/*"
                class="modern-upload"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <t-button
          theme="default"
          @click="()=>{this.$router.push('/platform/index')}"
          class="action-btn back-btn"
          size="large"
        >
          <span>返回</span>
        </t-button>
        <t-button
          theme="primary"
          type="submit"
          class="action-btn submit-btn"
          size="large"
        >
          <span>创建平台</span>
        </t-button>
      </div>
    </t-form>
  </div>
</template>
<script>
export default {

  data() {
    return {
      formData: {
        selectValue:'',
        mod_type:'1',
        powLimitId:'',
        pigeno_url:'',
        img_icon:[],
        add_type:0,
      },
      rules: {
        app_name: [{required: true, message: '请填写公众号/小程序名称', type: 'error'}],
        desc: [{required: true, message: '请填写公众号/小程序描述', type: 'error'}],
        app_id: [{required: true, message: '请输入AppId', type: 'error'}],
        app_secret: [{required: true, message: '请输入AppSecret', type: 'error'}],
      },
      list: [],
      userList:[],
      is:0,

    };
  },
  mounted() {
    this.getUserList();
    this.getUserIs();
    this.dianType(1);
  },
  methods: {
    selectPlatformType(type) {
      this.formData.mod_type = type;
      this.dianType(type);
    },
    selectUser(user_id){
      this.$request
        .post("/Platform/addAaminUserPlatform",{uid:user_id,type:this.formData.mod_type})
        .then((res) => {
          this.list = res.info;
          this.formData.powLimitId='';
          this.formData.add_type=res.add_type;
        })
        .catch((e) => {
          console.log(e);
        });
    },
    getUserIs(){
      this.$request
        .post("/Platform/getUserIs")
        .then((res) => {
          this.is=res;
        })
        .catch((e) => {
          console.log(e);
        });
    },
    dianType(d){
      this.formData.selectValue='';
      this.formData.powLimitId='';
      this.list = [];
      this.$request
        .post("/Platform/addPlatform",{type:d})
        .then((res) => {
          this.list = res.info;
          this.formData.add_type = res.add_type;
        })
        .catch((e) => {
          console.log(e);
        });
    },
    getUserList() {
      this.$request
        .post("/user/getUserMod",)
        .then((res) => {
          this.userList = res.data;
        })
        .catch((e) => {
          console.log(e);
        });
    },
    onSubmit({validateResult}) {
      if (validateResult === true) {
        this.$request
          .post("/Platform/addPlatformFrom",{from_data:this.formData})
          .then((res) => {
            if(res.code==200){
              this.$message.success(res.msg);
              setTimeout(()=>{
                this.$router.push('index');
              },1500)
            }else{
              this.$message.error(res.msg);
            }
          })
          .catch((e) => {
            console.log(e);
          });
      }
    },
    onReset() {

    },
    modulesChange(d){
      if(d.length==0){
        this.checkList=[];
      }else{
        var arr=[];
        d.forEach(i => {
          const result = this.list.find(item => item.value === i);
          arr.push(result);
        });
        this.checkList=arr;
      }
    },
  },

};
</script>
<style lang="less">
// ==================== 现代化SaaS设计变量 ====================
:root {
  --primary-color: #3b82f6;
  --primary-dark: #1e40af;
  --primary-light: #f3f4f6;
  --primary-lighter: #f8f9fa;
  --primary-border: #e9ecef;
  --primary-border-hover: #d1d5db;
  --success-color: #10b981;
  --success-dark: #059669;
  --warning-color: #d97706;
  --error-color: #dc2626;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --background-primary: #ffffff;
  --background-secondary: #f8f9fa;
  --background-form: #ffffff;
  --border-color: #e9ecef;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.04);
  --shadow-medium: 0 2px 8px rgba(0, 0, 0, 0.08);
  --shadow-heavy: 0 8px 25px rgba(0, 0, 0, 0.12);
  --radius-small: 8px;
  --radius-medium: 12px;
  --radius-large: 16px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
</style>

<style lang="less" scoped>
// ==================== 现代化表单设计系统 ====================

// 页面容器现代化
.modern-form-container {
  min-height: 100vh !important;
  background: var(--background-secondary);
  padding: 24px !important;
  position: relative !important;
}

// 页面标题区域现代化
.page-header {
  text-align: center !important;
  margin-bottom: 32px !important;
  position: relative !important;
  z-index: 1 !important;

  .page-title {
    font-size: 1.75rem !important;
    font-weight: 600 !important;
    color: var(--text-primary) !important;
    margin: 0 0 8px 0 !important;
    letter-spacing: -0.025em !important;

    @media (max-width: 768px) {
      font-size: 1.5rem !important;
    }
  }

  .page-subtitle {
    font-size: 0.875rem !important;
    color: var(--text-secondary) !important;
    margin: 0 !important;
    font-weight: 400 !important;
  }
}

// 步骤指示器
.steps-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 50px;
  position: relative;
  z-index: 1;

  .step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;

    .step-number {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: var(--background-primary);
      color: var(--text-secondary);
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 1.2rem;
      transition: var(--transition);
      border: 2px solid var(--border-color);
      box-shadow: var(--shadow-light);
    }

    .step-label {
      margin-top: 10px;
      color: var(--text-secondary);
      font-size: 0.9rem;
      font-weight: 500;
      transition: var(--transition);
    }

    &.active {
      .step-number {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
        box-shadow: 0 4px 12px rgba(33, 150, 243, 0.25);
      }

      .step-label {
        color: var(--text-primary);
        font-weight: 600;
      }
    }
  }

  .step-line {
    width: 80px;
    height: 2px;
    background: var(--border-color);
    margin: 0 20px;

    @media (max-width: 768px) {
      width: 40px;
      margin: 0 10px;
    }
  }
}

// 现代化表单
.modern-form {
  max-width: 80%;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

// 表单区块现代化
.form-section {
  background: var(--background-primary) !important;
  border-radius: var(--radius-large) !important;
  padding: 32px !important;
  margin-bottom: 24px !important;
  box-shadow: var(--shadow-medium) !important;
  border: 1px solid var(--border-color) !important;
  transition: var(--transition) !important;
  position: relative !important;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: var(--radius-large) var(--radius-large) 0 0;
  }

  &:hover {
    transform: translateY(-4px) !important;
    box-shadow: var(--shadow-heavy) !important;
    border-color: var(--primary-border-hover) !important;

    &::before {
      opacity: 1;
    }
  }

  @media (max-width: 768px) {
    padding: 20px !important;
  }
}

// 区块标题现代化
.section-header {
  margin-bottom: 24px;
  text-align: center;

  .section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 4px 0;
    letter-spacing: -0.025em;
  }

  .section-desc {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.875rem;
    font-weight: 500;
  }
}

// 表单网格优化
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
  align-items: start;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  @media (max-width: 1200px) {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

// 表单组优化
.form-group {
  position: relative;
  width: 100%;
  margin-bottom: 20px;

  &.full-width {
    grid-column: 1 / -1;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

// 现代化标签
.modern-label {
  display: block;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
  font-size: 0.875rem;
  letter-spacing: 0.025em;
}

// 简化输入框设计
.floating-input {
  position: relative;
  margin-bottom: 0;
  width: 100%;

  .floating-label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
    font-size: 0.875rem;
    letter-spacing: 0.025em;
  }

  .modern-input,
  .modern-select,
  .modern-datepicker {
    width: 100%;
    height: 48px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-small);
    padding: 12px 16px;
    font-size: 0.875rem;
    background: var(--background-primary);
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    color: var(--text-primary);

    &::placeholder {
      color: var(--text-secondary);
      font-weight: 400;
    }

    &:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    &:hover {
      border-color: var(--primary-border-hover);
    }
  }

  .input-icon {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.1rem;
    color: var(--text-secondary);
    transition: var(--transition);
    z-index: 5;
  }

  &:focus-within .input-icon {
    color: var(--primary-color);
  }
}

// 单选卡片组现代化
.radio-card-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.radio-card {
  background: var(--background-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-medium);
  padding: 24px;
  cursor: pointer;
  transition: var(--transition);
  position: relative;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: var(--shadow-light);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: var(--radius-medium) var(--radius-medium) 0 0;
  }

  &:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);

    &::before {
      opacity: 1;
    }
  }

  &.active {
    border-color: var(--primary-color);
    background: var(--primary-lighter);
    box-shadow: var(--shadow-medium);

    &::before {
      opacity: 1;
    }
  }

  .radio-content {
    flex: 1;

    h4 {
      margin: 0 0 4px 0;
      font-size: 1.125rem;
      font-weight: 600;
      color: var(--text-primary);
      letter-spacing: -0.025em;
    }

    p {
      margin: 0;
      font-size: 0.875rem;
      color: var(--text-secondary);
      font-weight: 500;
    }
  }

  .radio-check {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    color: transparent;
    transition: var(--transition);

    &.checked {
      background: var(--primary-color);
      border-color: var(--primary-color);
      color: white;
    }
  }
}

// 应用选择区域
.app-selection {
  margin-top: 15px;

  .app-radio-group {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
  }

  .app-option {
    background: white;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-medium);
    padding: 20px;
    transition: var(--transition);

    &:hover {
      border-color: var(--primary-color);
      box-shadow: var(--shadow-light);
    }

    .app-radio {
      width: 100%;

      .app-info {
        .app-name {
          font-weight: 500;
          color: var(--text-primary);
        }
      }
    }
  }

  .no-apps {
    text-align: center;
    padding: 40px;
    color: var(--text-secondary);

    .no-apps-icon {
      font-size: 3rem;
      margin-bottom: 15px;
    }

    p {
      margin: 0;
      font-size: 1.1rem;
    }
  }
}

// 上传区域
.upload-area {
  margin-top: 15px;

  .modern-upload {
    .t-upload__dragger {
      border: 2px dashed var(--border-color) !important;
      border-radius: var(--radius-medium) !important;
      background: var(--background-primary) !important;
      padding: 40px !important;
      transition: var(--transition) !important;

      &:hover {
        border-color: var(--primary-color) !important;
        background: var(--primary-light) !important;
      }
    }
  }
}

// 操作按钮区域
.form-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 40px;
  position: relative;
  z-index: 1;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
  }
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px !important;
  border-radius: var(--radius-medium) !important;
  font-weight: 600 !important;
  font-size: 0.875rem !important;
  transition: var(--transition) !important;
  border: none !important;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.025em !important;

  .btn-icon {
    font-size: 1rem;
  }

  &.submit-btn {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
    color: white !important;
    border: none !important;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2) !important;

    &:hover {
      background: linear-gradient(135deg, var(--primary-dark) 0%, #1e3a8a 100%) !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
    }

    &:active {
      transform: translateY(0) !important;
    }
  }

  &.back-btn {
    background: var(--primary-lighter) !important;
    color: var(--text-secondary) !important;
    border: 1px solid var(--border-color) !important;

    &:hover {
      background: #f1f3f4 !important;
      color: var(--text-primary) !important;
      border-color: var(--primary-border-hover) !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    }

    &:active {
      transform: translateY(0) !important;
    }
  }
}

// 动画效果
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.form-section {
  animation: slideInUp 0.6s ease-out;

  &:nth-child(2) { animation-delay: 0.1s; }
  &:nth-child(3) { animation-delay: 0.2s; }
  &:nth-child(4) { animation-delay: 0.3s; }
}

.page-header {
  animation: fadeIn 0.8s ease-out;
}

.steps-indicator {
  animation: slideInUp 0.6s ease-out 0.2s both;
}

// 响应式优化
@media (max-width: 768px) {
  .modern-form-container {
    padding: 20px 10px;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .radio-card-group {
    grid-template-columns: 1fr;
  }

  .steps-indicator {
    .step-item {
      .step-number {
        width: 40px;
        height: 40px;
        font-size: 1rem;
      }

      .step-label {
        font-size: 0.8rem;
      }
    }
  }
}

// TDesign组件样式现代化覆盖
:deep(.t-input) {
  border-radius: var(--radius-small) !important;
  border: 1px solid var(--border-color) !important;
  transition: var(--transition) !important;
  background: var(--background-primary) !important;
  box-shadow: var(--shadow-light) !important;
  height: 48px !important;
  font-size: 0.875rem !important;

  &:focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
  }

  &:hover {
    border-color: var(--primary-border-hover) !important;
  }

  .t-input__inner {
    height: 46px !important;
    line-height: 46px !important;
  }
}

:deep(.t-select) {
  .t-input {
    border-radius: var(--radius-small) !important;
    border: 1px solid var(--border-color) !important;
    transition: var(--transition) !important;
    background: var(--background-primary) !important;
    box-shadow: var(--shadow-light) !important;
    height: 48px !important;
    font-size: 0.875rem !important;

    &:focus {
      border-color: var(--primary-color) !important;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
    }

    &:hover {
      border-color: var(--primary-border-hover) !important;
    }

    .t-input__inner {
      height: 46px !important;
      line-height: 46px !important;
    }
  }
}

:deep(.t-date-picker) {
  .t-input {
    border-radius: var(--radius-small) !important;
    border: 1px solid var(--border-color) !important;
    transition: var(--transition) !important;
    background: var(--background-primary) !important;
    box-shadow: var(--shadow-light) !important;
    height: 48px !important;
    font-size: 0.875rem !important;

    &:focus {
      border-color: var(--primary-color) !important;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
    }

    &:hover {
      border-color: var(--primary-border-hover) !important;
    }

    .t-input__inner {
      height: 46px !important;
      line-height: 46px !important;
    }
  }
}

:deep(.t-radio-group) {
  .t-radio {
    margin-right: 20px !important;

    .t-radio__input {
      border: 2px solid var(--border-color) !important;
      transition: var(--transition) !important;

      &:checked {
        border-color: var(--primary-color) !important;
        background-color: var(--primary-color) !important;
      }
    }

    .t-radio__label {
      color: var(--text-secondary) !important;
      font-weight: 500 !important;
      transition: var(--transition) !important;
    }

    &:hover .t-radio__label {
      color: var(--primary-color) !important;
    }

    &.t-is-checked .t-radio__label {
      color: var(--primary-color) !important;
      font-weight: 600 !important;
    }
  }
}

:deep(.t-upload) {
  .t-upload__dragger {
    border: 2px dashed var(--border-color) !important;
    border-radius: var(--radius-medium) !important;
    background: var(--primary-lighter) !important;
    transition: var(--transition) !important;

    &:hover {
      border-color: var(--primary-color) !important;
      background: var(--primary-light) !important;
    }
  }
}

// 选择器样式现代化
.tdesign-demo__select-input-ul-single {
  display: flex;
  flex-direction: column;
  padding: 0;
  gap: 2px;
}

.tdesign-demo__select-input-ul-single > li {
  display: block;
  border-radius: var(--radius-small);
  line-height: 22px;
  cursor: pointer;
  padding: 12px 16px;
  color: var(--text-primary);
  transition: var(--transition);
  white-space: nowrap;
  word-wrap: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
}

.tdesign-demo__select-input-ul-single > li:hover {
  background-color: var(--primary-lighter);
  color: var(--primary-color);
}
</style>
